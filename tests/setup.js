require('dotenv').config({ path: '.env.test', quiet: true });

// Set test environment
process.env.NODE_ENV = 'test';

const { sequelize } = require('../src/models');

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test timeout
jest.setTimeout(3000);

// beforeEach(async () => {
//   // CLS (Continuation-Local Storage) is used to track the transaction
//   // across asynchronous operations. We start a new "context" for each test.
//   await namespace.runPromise(async () => {
//     const transaction = await sequelize.transaction();
//     namespace.set('transaction', transaction);
//   });
// });

// Clean up after each test
afterEach(async () => {
  jest.clearAllMocks();

  // // Retrieve the transaction from the CLS
  // const transaction = namespace.get('transaction');
  // if (transaction) {
  //   // Rollback the transaction to undo all changes made during the test
  //   await transaction.rollback();
  // }
});

beforeAll(async () => {
  // Ensure database connection is available
  try {
    await sequelize.authenticate();
  } catch (error) {
    console.error('Failed to connect to test database:', error);
    throw error;
  }
});

afterAll(async () => {
  // Close database connection after all tests
  try {
    await sequelize.close();
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
});
