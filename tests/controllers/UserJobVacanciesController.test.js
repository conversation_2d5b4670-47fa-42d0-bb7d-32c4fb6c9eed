const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const {
  UserFactory,
  UserProfileFactory,
  JobTitleFactory,
  JobVacancyFactory,
  UserJobVacancyFactory,
} = require('../factories');

describeWithTransaction('UserJobVacanciesController', () => {
  let admin;
  let user;
  let jobTitle;
  let jobVacancy1;
  let jobVacancy2;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();

    // Create job title and vacancies for testing
    jobTitle = await JobTitleFactory.create();
    jobVacancy1 = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
    jobVacancy2 = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
  });

  describe('GET /api/v1/user_job_vacancies', () => {
    beforeEach(async () => {
      // Create user profile for the test user
      await UserProfileFactory.create({
        user_id: user.id,
        current_position: {
          role_name: 'Software Engineer',
          department: 'Engineering',
          job_grade: 'L5',
        },
      });

      // Create some test recommendations
      await UserJobVacancyFactory.create({
        user_id: user.id,
        job_vacancy_id: jobVacancy1.id,
        competency_match: 0.85,
        skill_match: 0.9,
        status: 'recommended',
      });

      await UserJobVacancyFactory.create({
        user_id: user.id,
        job_vacancy_id: jobVacancy2.id,
        competency_match: 0.3,
        skill_match: 0.4,
        status: 'not_recommended',
      });
    });

    it('should return 401 if not authenticated', async () => {
      const response = await api.get('/api/v1/user_job_vacancies');
      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access token required');
    });

    it('should return 403 if authenticated as a non-admin user', async () => {
      const response = await api.as(user).get('/api/v1/user_job_vacancies');
      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Insufficient permissions');
    });

    it('should return all recommendations for admin without filters', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies');

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.pagination).toBeDefined();

      // Check response structure
      const recommendation = response.body.data[0];
      expect(recommendation).toHaveProperty('id');
      expect(recommendation).toHaveProperty('user');
      console.log(recommendation);
      expect(recommendation.user).toHaveProperty('id');
      expect(recommendation.user).toHaveProperty('name');
      expect(recommendation.user).toHaveProperty('current_position');
      expect(recommendation).toHaveProperty('competency_match');
      expect(recommendation).toHaveProperty('skill_match');
      expect(recommendation).toHaveProperty('status');
    });

    it('should filter recommendations by job_vacancy_id', async () => {
      const response = await api
        .as(admin)
        .get(`/api/v1/user_job_vacancies?job_vacancy_id=${jobVacancy1.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);

      const recommendation = response.body.data[0];
      expect(recommendation.competency_match).toBe(0.85);
      expect(recommendation.skill_match).toBe(0.9);
      expect(recommendation.status).toBe('recommended');
      expect(recommendation.user.current_position.role_name).toBe('Software Engineer');
    });

    it('should return empty array if job_vacancy_id has no recommendations', async () => {
      const newJobVacancy = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
      const response = await api
        .as(admin)
        .get(`/api/v1/user_job_vacancies?job_vacancy_id=${newJobVacancy.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(0);
    });

    it('should return 400 for invalid job_vacancy_id', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies?job_vacancy_id=invalid');

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Validation failed');
    });

    it('should filter by status', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies?status=recommended');

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0].status).toBe('recommended');
    });

    it('should support pagination', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies?page=1&limit=1');

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(1);
      expect(response.body.pagination.total).toBe(2);
    });

    it('should support sorting by competency_match', async () => {
      const response = await api
        .as(admin)
        .get('/api/v1/user_job_vacancies?sort=competency_match&sort_direction=asc');

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0].competency_match).toBe(0.3);
      expect(response.body.data[1].competency_match).toBe(0.85);
    });
  });

  describe('GET /api/v1/user_job_vacancies/:id', () => {
    let userJobVacancy;

    beforeEach(async () => {
      await UserProfileFactory.create({
        user_id: user.id,
        current_position: {
          role_name: 'Software Engineer',
          department: 'Engineering',
          job_grade: 'L5',
        },
      });

      userJobVacancy = await UserJobVacancyFactory.create({
        user_id: user.id,
        job_vacancy_id: jobVacancy1.id,
        competency_match: 0.85,
        skill_match: 0.9,
        status: 'recommended',
      });
    });

    it('should return 401 if not authenticated', async () => {
      const response = await api.get(`/api/v1/user_job_vacancies/${userJobVacancy.id}`);
      expect(response.status).toBe(401);
    });

    it('should return 403 if authenticated as a non-admin user', async () => {
      const response = await api.as(user).get(`/api/v1/user_job_vacancies/${userJobVacancy.id}`);
      expect(response.status).toBe(403);
    });

    it('should return specific recommendation for admin', async () => {
      const response = await api.as(admin).get(`/api/v1/user_job_vacancies/${userJobVacancy.id}`);

      expect(response.status).toBe(200);
      expect(response.body.data.id).toBe(userJobVacancy.id);
      expect(response.body.data.competency_match).toBe(0.85);
      expect(response.body.data.skill_match).toBe(0.9);
      expect(response.body.data.status).toBe('recommended');
      expect(response.body.data.user.id).toBe(user.id);
      expect(response.body.data.user.current_position.role_name).toBe('Software Engineer');
    });

    it('should return 404 for non-existent recommendation', async () => {
      const response = await api.as(admin).get('/api/v1/user_job_vacancies/99999');

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('User job vacancy recommendation not found');
    });
  });
});
