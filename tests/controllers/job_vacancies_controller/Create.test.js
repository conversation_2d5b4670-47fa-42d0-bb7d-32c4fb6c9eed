const api = require('../../utils/requestHelper');
const { describeWithTransaction } = require('../../utils/describeWithTransaction');
const GenerateJobDescService = require('../../../src/services/job_vacancy/GenerateJobDescService');

const { UserFactory, JobTitleFactory } = require('../../factories');

describeWithTransaction('JobVacanciesController - Create', () => {
  let admin;
  let user;
  let jobTitle;
  let mockGenerateJobDesc;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();
    jobTitle = await JobTitleFactory.create({ name: 'Software Engineer' });

    // Create mock function
    mockGenerateJobDesc = jest.fn().mockResolvedValue({
      jobTitle: 'Software Engineer',
      jobDesc: [
        'Develop and maintain software applications',
        'Collaborate with cross-functional teams',
        'Write clean, maintainable code',
      ],
      onetsocCodes: ['15-1132.00', '15-1133.00'],
    });

    // Mock GenerateJobDescService constructor
    GenerateJobDescService.mockImplementation(() => ({
      generateJobDesc: mockGenerateJobDesc,
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/v1/job_vacancies', () => {
    it('should create new job vacancy', async () => {
      const jobVacancyData = {
        job_title_id: jobTitle.id,
        department: 'Engineering',
        job_grade: 'L5',
      };

      const response = await api.as(admin).post('/api/v1/job_vacancies', jobVacancyData);

      if (response.status !== 201) {
        console.log('Response status:', response.status);
        console.log('Response body:', JSON.stringify(response.body, null, 2));
      }

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('name', 'Software Engineer');
    });

    it('should return 401 without authentication', async () => {
      const response = await api.post('/api/v1/job_vacancies');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 403 for non-admin users', async () => {
      const response = await api.as(user).post('/api/v1/job_vacancies');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });

    it('should generate job description using GenerateJobDescService', async () => {
      const jobVacancyData = {
        job_title_id: jobTitle.id,
        department: 'Engineering',
        job_grade: 'L5',
        reference_user_ids: [1, 2, 3],
      };

      const response = await api.as(admin).post('/api/v1/job_vacancies', jobVacancyData);

      if (response.status !== 201) {
        console.log('Generate job desc test - Response status:', response.status);
        console.log(
          'Generate job desc test - Response body:',
          JSON.stringify(response.body, null, 2),
        );
      }

      expect(response.status).toBe(201);
      expect(response.body.data).toHaveProperty('job_desc');
      expect(response.body.data).toHaveProperty('related_onetsoc_codes');
      expect(Array.isArray(response.body.data.job_desc)).toBe(true);
      expect(Array.isArray(response.body.data.related_onetsoc_codes)).toBe(true);
    });

    it('should handle job vacancy creation without reference_user_ids', async () => {
      const jobVacancyData = {
        job_title_id: jobTitle.id,
        department: 'Engineering',
        job_grade: 'L5',
      };

      const response = await api.as(admin).post('/api/v1/job_vacancies', jobVacancyData);

      if (response.status !== 201) {
        console.log('Without reference_user_ids test - Response status:', response.status);
        console.log(
          'Without reference_user_ids test - Response body:',
          JSON.stringify(response.body, null, 2),
        );
      }

      expect(response.status).toBe(201);
      expect(response.body.data).toHaveProperty('job_desc');
      expect(response.body.data).toHaveProperty('related_onetsoc_codes');
    });

    it('should return 400 for invalid job_title_id', async () => {
      const jobVacancyData = {
        job_title_id: 99999,
        department: 'Engineering',
        job_grade: 'L5',
      };

      const response = await api.as(admin).post('/api/v1/job_vacancies', jobVacancyData);

      if (response.status !== 400) {
        console.log('Invalid job_title_id test - Response status:', response.status);
        console.log(
          'Invalid job_title_id test - Response body:',
          JSON.stringify(response.body, null, 2),
        );
      }

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });
});
