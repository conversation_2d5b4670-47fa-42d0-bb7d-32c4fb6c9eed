const api = require('../utils/requestHelper');
const { describeWithTransaction } = require('../utils/describeWithTransaction');
const { UserFactory, JobTitleFactory, JobVacancyFactory } = require('../factories');
const { JobTitle, JobVacancy } = require('../../src/models');

describeWithTransaction('JobVacanciesController', () => {
  let admin;
  let user;

  beforeEach(async () => {
    admin = await UserFactory.create({ role: 'admin' });
    user = await UserFactory.create();
  });

  describe('GET /api/v1/job_vacancies', () => {
    beforeEach(async () => {
      const jobTitle = await JobTitleFactory.create();
      await JobVacancyFactory.create({ job_title_id: jobTitle.id });
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await api.get('/api/v1/job_vacancies');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await api.as(user).get('/api/v1/job_vacancies');

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('successful responses', () => {
      it('should return all job vacancies with correct format', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(response.body).toHaveProperty('pagination');
        expect(Array.isArray(response.body.data)).toBe(true);
        expect(response.body.data).toHaveLength(1);

        // Check the structure of job vacancy objects
        const jobVacancy = response.body.data[0];
        expect(jobVacancy).toHaveProperty('id');
        expect(jobVacancy).toHaveProperty('name');
      });

      it('should include pagination information', async () => {
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.body.pagination).toHaveProperty('page');
        expect(response.body.pagination).toHaveProperty('limit');
        expect(response.body.pagination).toHaveProperty('total');
        expect(response.body.pagination.total).toBe(1);
      });
    });

    describe('pagination and filtering', () => {
      beforeEach(async () => {
        const jobTitle = await JobTitle.findOne();
        await JobVacancyFactory.createMany(2, { job_title_id: jobTitle.id });
      });

      it('should support pagination', async () => {
        const params = { page: 1, limit: 2 };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.pagination.page).toBe(1);
        expect(response.body.pagination.limit).toBe(2);
        expect(response.body.pagination.total).toBe(3);
      });

      it('should support search filtering', async () => {
        const vc = await JobVacancy.findOne();
        await vc.update({ name: 'Vibe Coder' });

        const params = { search: 'Vibe' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('Vibe Coder');
      });

      it('should support sorting', async () => {
        const lastThree = await JobVacancy.findAll({ order: [['name', 'DESC']], limit: 3 });
        await lastThree[0].update({ name: 'Vibe Coder' });
        await lastThree[1].update({ name: 'Software Engineer' });
        await lastThree[2].update({ name: 'Product Engineer' });

        const params = { sort: 'name', sort_direction: 'desc' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.data[0].name).toBe('Vibe Coder');
        expect(response.body.data[1].name).toBe('Software Engineer');
        expect(response.body.data[2].name).toBe('Product Engineer');
      });
    });

    describe('empty results', () => {
      it('should return empty array when no job vacancies exist', async () => {
        await JobVacancy.destroy({ where: {}, truncate: true, cascade: true });
        const response = await api.as(admin).get('/api/v1/job_vacancies');

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
        expect(response.body.pagination.total).toBe(0);
      });

      it('should return empty array when filter matches nothing', async () => {
        const params = { search: 'NonexistentTitle' };
        const response = await api.as(admin).get('/api/v1/job_vacancies', params);

        expect(response.status).toBe(200);
        expect(response.body.data).toEqual([]);
      });
    });
  });

  describe('GET /api/v1/job_vacancies/:id', () => {
    let vacancy;

    beforeEach(async () => {
      const jobTitle = await JobTitleFactory.create();
      vacancy = await JobVacancyFactory.create({ job_title_id: jobTitle.id });
    });

    it('should return job vacancy for valid ID', async () => {
      const response = await api.as(admin).get(`/api/v1/job_vacancies/${vacancy.id}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('id', vacancy.id);
    });

    it('should return 404 for non-existent job vacancy', async () => {
      const response = await api.as(admin).get('/api/v1/job_vacancies/0');

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Job vacancy not found');
    });

    it('should return 401 without authentication', async () => {
      const response = await api.get(`/api/v1/job_vacancies/${vacancy.id}`);

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should return 403 for non-admin users', async () => {
      const response = await api.as(user).get(`/api/v1/job_vacancies/${vacancy.id}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });
});
