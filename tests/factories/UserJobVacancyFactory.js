'use strict';

const BaseFactory = require('./BaseFactory');
const { UserJobVacancy } = require('../../src/models');

class UserJobVacancyFactory extends BaseFactory {
  constructor() {
    super(UserJobVacancy);
  }

  static defaultAttributes() {
    return {
      competency_match: this.faker.number.float({ min: 0, max: 1, fractionDigits: 2 }),
      skill_match: this.faker.number.float({ min: 0, max: 1, fractionDigits: 2 }),
      status: this.faker.helpers.arrayElement(['recommended', 'not_recommended']),
    };
  }

  static traits() {
    return {
      recommended: {
        status: 'recommended',
        competency_match: this.faker.number.float({ min: 0.7, max: 1, fractionDigits: 2 }),
        skill_match: this.faker.number.float({ min: 0.7, max: 1, fractionDigits: 2 }),
      },
      not_recommended: {
        status: 'not_recommended',
        competency_match: this.faker.number.float({ min: 0, max: 0.5, fractionDigits: 2 }),
        skill_match: this.faker.number.float({ min: 0, max: 0.5, fractionDigits: 2 }),
      },
    };
  }
}

module.exports = UserJobVacancyFactory;
