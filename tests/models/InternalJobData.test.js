const { InternalJobProfileData } = require('../../src/models');
const { describeWithTransaction } = require('../utils/describeWithTransaction');

describeWithTransaction('InternalJobProfileData Model', () => {
  describe('validations', () => {
    it('should create a valid internal job data with all fields', async () => {
      const internalJobData = {
        job_division: 'Technology',
        job_group: 'Software Development',
        position_name: 'Senior Software Engineer',
        job_classification: 'Professional',
        job_family: 'Engineering',
        sub_job_family: 'Backend Development',
        main_responsibilities:
          'Design and develop scalable backend systems, mentor junior developers, and collaborate with cross-functional teams.',
        work_input:
          'Requirements documents, technical specifications, user stories, and feedback from stakeholders.',
        work_output: 'High-quality code, technical documentation',
        success_criteria:
          'Successful delivery of features on time, code quality metrics, and positive team feedback.',
        requirement:
          "Bachelor's degree in Computer Science, 5+ years of experience in software development, proficiency in Node.js and databases.",
        competency:
          'Technical expertise, problem-solving skills, communication skills, leadership abilities, and continuous learning mindset.',
      };

      const jobData = await InternalJobProfileData.create(internalJobData);

      expect(jobData.id).toBeDefined();
      expect(jobData.job_division).toBe('Technology');
      expect(jobData.job_group).toBe('Software Development');
      expect(jobData.position_name).toBe('Senior Software Engineer');
      expect(jobData.job_classification).toBe('Professional');
      expect(jobData.job_family).toBe('Engineering');
      expect(jobData.sub_job_family).toBe('Backend Development');
      expect(jobData.main_responsibilities).toContain(
        'Design and develop scalable backend systems',
      );
      expect(jobData.work_input).toContain('Requirements documents');
      expect(jobData.work_output).toBe('High-quality code, technical documentation');
      expect(jobData.success_criteria).toContain('Successful delivery of features');
      expect(jobData.requirement).toContain("Bachelor's degree");
      expect(jobData.competency).toContain('Technical expertise');
      expect(jobData.created_at).toBeDefined();
      expect(jobData.updated_at).toBeDefined();
    });

    it('should create internal job data with minimal fields', async () => {
      const internalJobData = {
        position_name: 'Junior Developer',
      };

      const jobData = await InternalJobProfileData.create(internalJobData);

      expect(jobData.id).toBeDefined();
      expect(jobData.position_name).toBe('Junior Developer');
      expect(jobData.job_division).toBeNull();
      expect(jobData.job_group).toBeNull();
      expect(jobData.created_at).toBeDefined();
      expect(jobData.updated_at).toBeDefined();
    });

    it('should create internal job data with null values', async () => {
      const internalJobData = {
        job_division: null,
        job_group: null,
        position_name: null,
        job_classification: null,
        job_family: null,
        sub_job_family: null,
        main_responsibilities: null,
        work_input: null,
        work_output: null,
        success_criteria: null,
        requirement: null,
        competency: null,
      };

      const jobData = await InternalJobProfileData.create(internalJobData);

      expect(jobData.id).toBeDefined();
      expect(jobData.job_division).toBeNull();
      expect(jobData.position_name).toBeNull();
    });

    it('should fail validation if position_name exceeds 255 characters', async () => {
      const longString = 'A'.repeat(256); // 256 characters

      const internalJobData = {
        position_name: longString,
      };

      await expect(InternalJobProfileData.create(internalJobData)).rejects.toThrow();
    });

    it('should fail validation if work_output exceeds 255 characters', async () => {
      const longString = 'A'.repeat(256); // 256 characters

      const internalJobData = {
        work_output: longString,
      };

      await expect(InternalJobProfileData.create(internalJobData)).rejects.toThrow();
    });

    it('should accept long text in TEXT fields', async () => {
      const longText = 'A'.repeat(1000); // 1000 characters

      const internalJobData = {
        main_responsibilities: longText,
        work_input: longText,
        success_criteria: longText,
        requirement: longText,
        competency: longText,
      };

      const jobData = await InternalJobProfileData.create(internalJobData);

      expect(jobData.main_responsibilities).toBe(longText);
      expect(jobData.work_input).toBe(longText);
      expect(jobData.success_criteria).toBe(longText);
      expect(jobData.requirement).toBe(longText);
      expect(jobData.competency).toBe(longText);
    });
  });

  describe('hooks', () => {
    it('should set created_at and updated_at on create', async () => {
      const beforeCreate = new Date();

      const jobData = await InternalJobProfileData.create({
        position_name: 'Product Manager',
        job_division: 'Product',
      });

      const afterCreate = new Date();

      expect(jobData.created_at).toBeDefined();
      expect(jobData.updated_at).toBeDefined();
      expect(jobData.created_at.getTime()).toBeGreaterThanOrEqual(beforeCreate.getTime());
      expect(jobData.created_at.getTime()).toBeLessThanOrEqual(afterCreate.getTime());
      expect(jobData.updated_at.getTime()).toBeGreaterThanOrEqual(beforeCreate.getTime());
      expect(jobData.updated_at.getTime()).toBeLessThanOrEqual(afterCreate.getTime());
    });

    it('should update updated_at on update', async () => {
      const jobData = await InternalJobProfileData.create({
        position_name: 'Marketing Manager',
        job_division: 'Marketing',
      });

      const originalUpdatedAt = jobData.updated_at;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));

      jobData.position_name = 'Senior Marketing Manager';
      await jobData.save();

      expect(jobData.updated_at.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe('model behavior', () => {
    it('should handle complex job profile data', async () => {
      const complexJobData = {
        job_division: 'Technology',
        job_group: 'Software Engineering',
        position_name: 'Staff Software Engineer',
        job_classification: 'Senior Professional',
        job_family: 'Engineering',
        sub_job_family: 'Full Stack Development',
        main_responsibilities: `Lead the design and implementation of complex software systems.
Mentor and guide junior and mid-level engineers.
Collaborate with product managers and designers to define technical requirements.
Ensure code quality and best practices across the team.`,
        work_input: `Product requirements documents, user stories, technical specifications, design mockups, stakeholder feedback, performance metrics, and industry best practices.`,
        work_output: 'Scalable software solutions, technical documentation, code reviews',
        success_criteria: `Successful delivery of high-quality features within deadlines.
Positive impact on team productivity and code quality.
Effective mentorship of junior team members.
Contribution to technical strategy and architecture decisions.`,
        requirement: `Master's degree in Computer Science or equivalent experience.
8+ years of software development experience.
Expertise in multiple programming languages and frameworks.
Strong system design and architecture skills.
Excellent communication and leadership abilities.`,
        competency: `Advanced technical skills in software development.
System design and architecture expertise.
Leadership and mentoring capabilities.
Strong problem-solving and analytical thinking.
Excellent communication and collaboration skills.
Continuous learning and adaptation to new technologies.`,
      };

      const jobData = await InternalJobProfileData.create(complexJobData);

      expect(jobData.job_division).toBe('Technology');
      expect(jobData.main_responsibilities).toContain('Lead the design and implementation');
      expect(jobData.requirement).toContain("Master's degree");
    });

    it('should find internal job data by position name', async () => {
      await InternalJobProfileData.create({
        position_name: 'Data Scientist',
        job_division: 'Data & Analytics',
        job_family: 'Data Science',
      });

      const foundJobData = await InternalJobProfileData.findOne({
        where: { position_name: 'Data Scientist' },
      });

      expect(foundJobData).toBeDefined();
      expect(foundJobData.position_name).toBe('Data Scientist');
      expect(foundJobData.job_division).toBe('Data & Analytics');
    });

    it('should find internal job data by job division and group', async () => {
      await InternalJobProfileData.create({
        job_division: 'Engineering',
        job_group: 'Frontend',
        position_name: 'Frontend Developer',
      });

      const foundJobData = await InternalJobProfileData.findOne({
        where: {
          job_division: 'Engineering',
          job_group: 'Frontend',
        },
      });

      expect(foundJobData).toBeDefined();
      expect(foundJobData.position_name).toBe('Frontend Developer');
    });

    it('should count internal job data correctly', async () => {
      await InternalJobProfileData.bulkCreate([
        {
          position_name: 'UX Designer',
          job_division: 'Design',
          job_family: 'User Experience',
        },
        {
          position_name: 'DevOps Engineer',
          job_division: 'Engineering',
          job_family: 'Infrastructure',
        },
        {
          position_name: 'Sales Representative',
          job_division: 'Sales',
          job_family: 'Business Development',
        },
      ]);

      const count = await InternalJobProfileData.count();
      expect(count).toBe(3);
    });

    it('should update specific fields without affecting others', async () => {
      const jobData = await InternalJobProfileData.create({
        position_name: 'Software Engineer',
        job_division: 'Technology',
        main_responsibilities: 'Develop software applications',
        requirement: "Bachelor's degree required",
      });

      // Update only one field
      await jobData.update({
        main_responsibilities: 'Develop and maintain software applications',
      });

      await jobData.reload();

      expect(jobData.main_responsibilities).toBe('Develop and maintain software applications');
      expect(jobData.position_name).toBe('Software Engineer');
      expect(jobData.job_division).toBe('Technology');
      expect(jobData.requirement).toBe("Bachelor's degree required");
    });
  });
});
