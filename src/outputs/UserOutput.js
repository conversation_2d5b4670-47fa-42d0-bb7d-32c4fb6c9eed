const ApiOutput = require('./ApiOutput');

/**
 * Output formatting class for user responses
 */
class UserOutput extends ApiOutput {
  /**
   * Format the user output data
   * @returns {Object} Formatted user data
   */
  format() {
    return {
      id: this.data.id,
      name: this.data.name,
      email: this.data.email,
      role: this.data.role,
    };
  }

  showFormat() {
    const user = this.data;
    const profile = user.profile || {};

    const performanceReviews =
      user.positions?.flatMap(position =>
        position.performanceReviews.map(review => ({
          id: review.id,
          review_type: review.review_type,
          review_result: review.review_result,
          user_position: position,
          created_at: review.created_at,
          updated_at: review.updated_at,
        })),
      ) || [];

    // sort by latest starts_at
    const currentPosition =
      user.positions?.sort((a, b) => {
        return new Date(b.starts_at) - new Date(a.starts_at);
      })[0] || {};

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      current_position: currentPosition,
      phone_number: profile.phone_number,
      location: profile.location,
      manager: profile.manager,
      years_experience: profile.years_experience,
      performance_rating: profile.performance_rating,
      last_promotion: profile.last_promotion,
      education: profile.education,
      competencies: profile.competencies || [],
      skills: profile.skills || [],
      assessment_results: user.userAssessmentResults || [],
      positions: user.positions || [],
      performance_reviews: performanceReviews,
    };
  }
}

module.exports = UserOutput;
