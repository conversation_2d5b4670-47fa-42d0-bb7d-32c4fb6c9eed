'use strict';
const { Model } = require('sequelize');
const { sequelize } = require('../config/sequelize');
const { allSchemas, allIndexes } = require('./schema');

/**
 * AppModel - Base model class that handles Sequelize complexity
 * Similar to Rails ActiveRecord, child classes only need to define:
 * - static schema() - returns the model attributes
 * - static associate(models) - defines associations (optional)
 * - static hooks() - defines lifecycle hooks (optional)
 * - static options() - additional model options (optional)
 */
class AppModel extends Model {
  /**
   * Initialize a model class with schema, associations, and hooks
   * This method should be called by child classes
   */
  static initModel(modelName, childClass) {
    let tableName = '';
    const hooks = childClass.hooks ? childClass.hooks() : {};
    const additionalOptions = childClass.options ? childClass.options() : {};

    if (additionalOptions.tableName) {
      tableName = additionalOptions.tableName;
    } else {
      tableName = modelName
        .replace(/([A-Z])/g, '_$1')
        .toLowerCase()
        .replace(/^_/, '')
        .concat('s');
    }

    let childSchema = childClass.schema();
    if (typeof childSchema !== 'object') {
      childSchema = {};
    }

    const indexes = allIndexes[tableName] || [];
    const schema = { ...allSchemas[tableName], ...childSchema };

    // Default options that provide Rails-like behavior
    const defaultOptions = {
      sequelize,
      modelName,
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      tableName,
      hooks: {
        // Default hook to update updated_at timestamp
        beforeUpdate: instance => {
          instance.updated_at = new Date();
        },
        // Merge with custom hooks
        ...hooks,
      },
      indexes,
    };

    // Merge options
    const options = { ...defaultOptions, ...additionalOptions };

    // Initialize the model
    childClass.init(schema, options);
    return childClass;
  }
}

module.exports = AppModel;
